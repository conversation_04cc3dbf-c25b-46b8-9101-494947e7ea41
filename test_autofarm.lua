--[[
Test script to verify the auto farm functionality
This script tests the core auto farm logic without the full UI
]]

-- Mock workspace structure for testing (updated to match Important.Plants_Physical structure)
local mockWorkspace = {
    Farm = {
        Farm1 = {
            Important = {
                Plants_Physical = {
                    Plant1 = {
                        Name = "TestPlant1",
                        FindFirstChild = function(self, name, recursive)
                            if name == "ProximityPrompt" then
                                return { Name = "ProximityPrompt" }
                            end
                            return nil
                        end,
                        IsA = function(self, className) return className == "Model" end
                    },
                    Plant2 = {
                        Name = "TestPlant2",
                        FindFirstChild = function(self, name, recursive) return nil end,
                        IsA = function(self, className) return className == "Model" end
                    }
                }
            }
        },
        Farm2 = {
            Important = {
                Plants_Physical = {
                    Plant3 = {
                        Name = "TestPlant3",
                        FindFirstChild = function(self, name, recursive)
                            if name == "ProximityPrompt" then
                                return { Name = "ProximityPrompt" }
                            end
                            return nil
                        end,
                        IsA = function(self, className) return className == "Model" end
                    }
                }
            }
        }
    }
}

-- Mo<PERSON> functions
local function fireproximityprompt(prompt)
    print("Harvesting plant with prompt:", prompt.Name)
end

-- Auto Farm System Variables (simplified)
local AutoFarmSystem = {
    isActive = true,
    harvestCount = 0,
    lastScanTime = 0
}

-- Harvest function for plants
local function HarvestPlant(Plant)
    if not Plant or not Plant:IsA("Model") then return false end
    
    local Prompt = Plant:FindFirstChild("ProximityPrompt", true)
    
    -- Check if it can be harvested
    if not Prompt then 
        print("No ProximityPrompt found for plant:", Plant.Name)
        return false 
    end
    
    pcall(function()
        fireproximityprompt(Prompt)
        AutoFarmSystem.harvestCount = AutoFarmSystem.harvestCount + 1
        print("Successfully harvested plant:", Plant.Name)
    end)
    
    return true
end

-- Scan all farm areas for harvestable plants (using mock workspace)
local function ScanAndHarvestFarms()
    if not AutoFarmSystem.isActive then return end
    
    AutoFarmSystem.lastScanTime = os.time()
    local harvestedThisScan = 0
    
    print("Starting farm scan...")
    
    pcall(function()
        local farmFolder = mockWorkspace.Farm
        if not farmFolder then 
            print("No Farm folder found")
            return 
        end
        
        -- Iterate through all farm subfolders
        for farmName, farmSubfolder in pairs(farmFolder) do
            if not AutoFarmSystem.isActive then break end

            print("Scanning farm:", farmName)

            -- Look for Important folder first, then Plants_Physical inside it
            local importantFolder = farmSubfolder.Important
            if importantFolder then
                print("Found Important folder in", farmName)
                local plantsPhysical = importantFolder.Plants_Physical
                if plantsPhysical then
                    print("Found Plants_Physical in", farmName)

                    -- Check each child in Plants_Physical for models
                    for plantName, child in pairs(plantsPhysical) do
                        if not AutoFarmSystem.isActive then break end

                        if child:IsA("Model") then
                            print("Found plant model:", plantName)
                            -- Attempt to harvest this plant
                            if HarvestPlant(child) then
                                harvestedThisScan = harvestedThisScan + 1
                            end
                        end
                    end
                else
                    print("No Plants_Physical found in Important folder of", farmName)
                end
            else
                print("No Important folder found in", farmName)
            end
        end
    end)
    
    print("Scan complete. Harvested", harvestedThisScan, "plants this scan.")
    print("Total harvested:", AutoFarmSystem.harvestCount)
    return harvestedThisScan
end

-- Test the auto farm system
print("=== Auto Farm System Test ===")
print("Testing harvest functionality...")

-- Run a test scan
ScanAndHarvestFarms()

print("\n=== Test Results ===")
print("Total plants harvested:", AutoFarmSystem.harvestCount)
print("Expected: 2 plants (Plant1 and Plant3 have ProximityPrompts)")
print("Test", AutoFarmSystem.harvestCount == 2 and "PASSED" or "FAILED")
